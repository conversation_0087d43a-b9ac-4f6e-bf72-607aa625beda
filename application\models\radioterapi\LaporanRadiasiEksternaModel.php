<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanRadiasiEksternaModel extends CI_Model
{
    var $table = 'medis.radiasi_eksterna';
    var $column_order = array(null, 'lokasi', 'oleh', 'updated_at');
    var $column_search = array('lokasi', 'oleh', 'updated_at');
    var $order = array('id' => 'asc');

    private function _get_datatables_query($nokun)
    {
        $this->db->from($this->table);
        $this->db->where('nokun', $nokun);
        $this->db->where('status', 1);

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_GET['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_GET['search']['value']);
                } else {
                    $this->db->or_like($item, $_GET['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_GET['order'])) {
            $this->db->order_by($this->column_order[$_GET['order']['0']['column']], $_GET['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables($nokun)
    {
        $this->_get_datatables_query($nokun);
        if ($_GET['length'] != -1)
            $this->db->limit($_GET['length'], $_GET['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered($nokun)
    {
        $this->_get_datatables_query($nokun);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all($nokun)
    {
        $this->db->from($this->table);
        $this->db->where('nokun', $nokun);
        $this->db->where('status', 1);
        return $this->db->count_all_results();
    }

    var $table_detail = 'medis.radiasi_eksterna_detail';
    var $column_order_detail = array(null, 'tanggal', 'energi', 'ssd_sad', 'dosis_fraksi', 'oleh');
    var $column_search_detail = array('tanggal', 'energi', 'ssd_sad', 'dosis_fraksi', 'oleh');
    var $order_detail = array('id' => 'asc');

    private function _get_datatables_query_detail($id_radiasi)
    {
        $this->db->from($this->table_detail);
        $this->db->where('id_radiasi', $id_radiasi);
        $this->db->where('status', 1);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_GET['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_GET['search']['value']);
                } else {
                    $this->db->or_like($item, $_GET['search']['value']);
                }
                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_GET['order'])) {
            $this->db->order_by($this->column_order_detail[$_GET['order']['0']['column']], $_GET['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail($id_radiasi)
    {
        $this->_get_datatables_query_detail($id_radiasi);
        if ($_GET['length'] != -1)
            $this->db->limit($_GET['length'], $_GET['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail($id_radiasi)
    {
        $this->_get_datatables_query_detail($id_radiasi);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_detail($id_radiasi)
    {
        $this->db->from($this->table_detail);
        $this->db->where('id_radiasi', $id_radiasi);
        $this->db->where('status', 1);
        return $this->db->count_all_results();
    }

    public function save($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    /**
     * Get data radiasi eksterna by ID
     */
    public function get_by_id($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status', 1);
        $query = $this->db->get($this->table);
        return $query->row();
    }

    /**
     * Update data radiasi eksterna
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Update data radiasi eksterna detail
     */
    public function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table_detail, $data);
    }

    /**
     * Get nama user berdasarkan ID
     */
    public function get_nama_user($user_id)
    {
        $this->db->select('NAMA');
        $this->db->from('aplikasi.pengguna');
        $this->db->where('ID', $user_id);
        $query = $this->db->get();
        $result = $query->row();
        return $result ? $result->NAMA : 'Unknown';
    }

    /**
     * Soft delete radiasi eksterna
     */
    public function delete($id)
    {
        $data = array('status' => 0);
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Soft delete radiasi eksterna detail
     */
    public function delete_detail($id)
    {
        $data = array('status' => 0);
        $this->db->where('id', $id);
        return $this->db->update($this->table_detail, $data);
    }
}
