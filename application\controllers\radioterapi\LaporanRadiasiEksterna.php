<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanRadiasiEksterna extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        // Validasi akses - hanya radiografer (profesi = 8) yang boleh akses
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('radioterapi/LaporanRadiasiEksternaModel', 'model');
    }

    /**
     * Validasi apakah user adalah radiografer
     */
    private function _validate_radiografer()
    {
        if ($this->session->userdata('profesi') != 8) {
            echo json_encode(['status' => FALSE, 'message' => 'Akses ditolak. Hanya radiografer yang dapat melakukan input.']);
            exit;
        }
    }

    public function index()
    {
        $data['nokun'] = $this->uri->segment(6);
        $this->load->view('Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna', $data);
    }

    public function get_data()
    {
        $nokun = $this->input->get('nokun');
        $list = $this->model->get_datatables($nokun);
        $data = array();
        foreach ($list as $field) {
            $row = array();
            $row[] = $field->id;
            $row[] = $field->lokasi;
            $row[] = $this->model->get_nama_user($field->oleh); // Tampilkan nama user
            $row[] = date('d-m-Y H:i', strtotime($field->updated_at));

            // Tombol aksi hanya untuk radiografer
            if ($this->session->userdata('profesi') == 8) {
                $row[] = '<button type="button" class="btn btn-primary btn-sm edit-btn" data-id="' . $field->id . '" title="Detail"><i class="fa fa-edit"></i></button>';
            } else {
                $row[] = '<button type="button" class="btn btn-secondary btn-sm" disabled title="Hanya radiografer yang dapat mengedit"><i class="fa fa-lock"></i></button>';
            }
            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all($nokun),
            "recordsFiltered" => $this->model->count_filtered($nokun),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function get_detail_data()
    {
        $id_radiasi = $this->input->get('id_radiasi');
        $list = $this->model->get_datatables_detail($id_radiasi);
        $data = array();
        foreach ($list as $field) {
            $row = array();
            $row[] = $field->id;
            $row[] = date('d-m-Y', strtotime($field->tanggal));
            $row[] = $field->energi;
            $row[] = $field->ssd_sad;
            $row[] = $field->dosis_fraksi;
            $row[] = $this->model->get_nama_user($field->oleh); // Tampilkan nama user
            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all_detail($id_radiasi),
            "recordsFiltered" => $this->model->count_filtered_detail($id_radiasi),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function save()
    {
        // Validasi hanya radiografer yang boleh input
        $this->_validate_radiografer();

        // Validasi input
        if (empty($this->input->post('nokun')) || empty($this->input->post('lokasi'))) {
            echo json_encode(['status' => FALSE, 'message' => 'Data tidak lengkap']);
            return;
        }

        $data = array(
            'nokun' => $this->input->post('nokun'),
            'lokasi' => $this->input->post('lokasi'),
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );

        $result = $this->model->save($data);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Data berhasil disimpan']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menyimpan data']);
        }
    }

    public function save_detail()
    {
        // Validasi hanya radiografer yang boleh input
        $this->_validate_radiografer();

        // Validasi input
        $required_fields = ['id_radiasi', 'tanggal', 'energi', 'ssd_sad', 'dosis_fraksi'];
        foreach ($required_fields as $field) {
            if (empty($this->input->post($field))) {
                echo json_encode(['status' => FALSE, 'message' => 'Data tidak lengkap']);
                return;
            }
        }

        $data = array(
            'id_radiasi' => $this->input->post('id_radiasi'),
            'tanggal' => $this->input->post('tanggal'),
            'energi' => $this->input->post('energi'),
            'ssd_sad' => $this->input->post('ssd_sad'),
            'dosis_fraksi' => $this->input->post('dosis_fraksi'),
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );

        $result = $this->model->save_detail($data);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Detail berhasil disimpan']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menyimpan detail']);
        }
    }

    /**
     * Get data radiasi by ID untuk modal
     */
    public function get_radiasi_by_id()
    {
        $id = $this->input->get('id');
        $data = $this->model->get_by_id($id);
        echo json_encode($data);
    }
}
