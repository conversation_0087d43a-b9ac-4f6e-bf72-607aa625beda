<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanRadiasiEksterna extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('radioterapi/LaporanRadiasiEksternaModel', 'model');
    }

    public function index()
    {
        $data['nokun'] = $this->uri->segment(6);
        $this->load->view('Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna', $data);
    }

    public function get_data()
    {
        $nokun = $this->input->get('nokun');
        $list = $this->model->get_datatables($nokun);
        $data = array();
        foreach ($list as $field) {
            $row = array();
            $row[] = $field->id;
            $row[] = $field->lokasi;
            $row[] = $field->oleh;
            $row[] = $field->updated_at;
            $row[] = '<button type="button" class="btn btn-primary btn-sm edit-btn" data-id="' . $field->id . '">Edit</button>';
            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all($nokun),
            "recordsFiltered" => $this->model->count_filtered($nokun),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function get_detail_data()
    {
        $id_radiasi = $this->input->get('id_radiasi');
        $list = $this->model->get_datatables_detail($id_radiasi);
        $data = array();
        foreach ($list as $field) {
            $row = array();
            $row[] = $field->id;
            $row[] = $field->tanggal;
            $row[] = $field->energi;
            $row[] = $field->ssd_sad;
            $row[] = $field->dosis_fraksi;
            $row[] = $field->oleh;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all_detail($id_radiasi),
            "recordsFiltered" => $this->model->count_filtered_detail($id_radiasi),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function save()
    {
        $data = array(
            'nokun' => $this->input->post('nokun'),
            'lokasi' => $this->input->post('lokasi'),
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );
        $this->model->save($data);
        echo json_encode(['status' => TRUE]);
    }

    public function save_detail()
    {
        $data = array(
            'id_radiasi' => $this->input->post('id_radiasi'),
            'tanggal' => $this->input->post('tanggal'),
            'energi' => $this->input->post('energi'),
            'ssd_sad' => $this->input->post('ssd_sad'),
            'dosis_fraksi' => $this->input->post('dosis_fraksi'),
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );
        $this->model->save_detail($data);
        echo json_encode(['status' => TRUE]);
    }
}
