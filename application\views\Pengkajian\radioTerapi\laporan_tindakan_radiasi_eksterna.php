<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4><PERSON><PERSON><PERSON> Radiasi Eksterna</h4>
                </div>
                <div class="card-body">
                    <?php if ($this->session->userdata('profesi') == 8) : ?>
                        <form id="form-radiasi-eksterna">
                            <input type="hidden" name="nokun" value="<?= $nokun ?>">
                            <div class="form-group">
                                <label for="lokasi">Lokasi</label>
                                <input type="text" class="form-control" id="lokasi" name="lokasi" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Data Radiasi Eksterna</h4>
                </div>
                <div class="card-body">
                    <table id="tabel-radiasi-eksterna" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Lokasi</th>
                                <th>Oleh</th>
                                <th>Updated At</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-detail-label">Detail Radiasi Eksterna</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php if ($this->session->userdata('profesi') == 8) : ?>
                    <form id="form-radiasi-eksterna-detail">
                        <input type="hidden" name="id_radiasi" id="id_radiasi">
                        <div class="form-group">
                            <label for="tanggal">Tanggal</label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                        </div>
                        <div class="form-group">
                            <label for="energi">Energi</label>
                            <input type="text" class="form-control" id="energi" name="energi" required>
                        </div>
                        <div class="form-group">
                            <label for="ssd_sad">SSD/SAD</label>
                            <input type="text" class="form-control" id="ssd_sad" name="ssd_sad" required>
                        </div>
                        <div class="form-group">
                            <label for="dosis_fraksi">Dosis/Fraksi</label>
                            <input type="text" class="form-control" id="dosis_fraksi" name="dosis_fraksi" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Simpan Detail</button>
                    </form>
                <?php endif; ?>
                <hr>
                <table id="tabel-radiasi-eksterna-detail" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tanggal</th>
                            <th>Energi</th>
                            <th>SSD/SAD</th>
                            <th>Dosis/Fraksi</th>
                            <th>Oleh</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
